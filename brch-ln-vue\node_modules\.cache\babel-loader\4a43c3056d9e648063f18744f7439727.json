{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\oilAccount\\uploadFileModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\oilAccount\\uploadFileModal.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["uploadFileModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,OAAA,KAAA,MAAA,oBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,iBADA;AAEA,EAAA,UAAA,EAAA,EAFA;AAGA,EAAA,KAAA,EAAA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,KADA;AAEA,MAAA,OAAA,EAAA,oBAAA;AACA,eAAA,CAAA,KAAA,EAAA,MAAA,EAAA,KAAA,EAAA,KAAA,EAAA,KAAA,EAAA,MAAA,EAAA,KAAA,EAAA,KAAA,EAAA,KAAA,EAAA,KAAA,EAAA,KAAA,CAAA;AACA;AAJA;AADA,GAHA;AAWA,EAAA,IAXA,kBAWA;AAAA;;AACA,WAAA;AACA,MAAA,MAAA,EAAA,IADA;AAEA,MAAA,WAAA,EAAA,GAFA;AAGA,MAAA,IAAA,EAAA,KAHA;AAIA,MAAA,OAAA,EAAA,IAJA;AAKA,MAAA,OAAA,EAAA,KALA;AAMA,MAAA,OAAA,EAAA,KANA;AAOA,MAAA,KAAA,EAAA,kBAPA;AAQA,MAAA,MAAA,EAAA;AACA,QAAA,QAAA,EAAA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAIA,QAAA,UAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,YAAA,EAAA,MAHA;AAIA,UAAA,KAAA,EAAA,GAJA;AAKA,UAAA,MAAA,EAAA,KAAA;AALA,SADA;AAJA,OARA;AAsBA,MAAA,OAAA,EAAA,CACA;AACA,QAAA,GAAA,EAAA,UADA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CAAA,GAAA,EAAA;AACA,YAAA,EAAA,EAAA;AACA,cAAA,KAAA,EAAA,iBAAA;AACA,gBAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,GAAA;AACA;AAHA;AADA,WAAA,EAMA,MAAA,CAAA,GAAA,CAAA,QANA,CAAA;AAOA;AAVA,OADA,EAaA;AAAA,QAAA,GAAA,EAAA,aAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAbA,EAcA;AAAA,QAAA,GAAA,EAAA,YAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAdA,EAeA;AAAA,QAAA,GAAA,EAAA,UAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAfA,EAgBA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,GAAA,EAAA,QAFA;AAGA,QAAA,KAAA,EAAA,GAHA;AAIA,QAAA,KAAA,EAAA,QAJA;AAKA,QAAA,KAAA,EAAA,OALA;AAMA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,cAAA,IAAA,GACA,CAAA,CAAA,QAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,IAAA,EAAA;AAFA,aADA;AAKA,YAAA,KAAA,EAAA;AACA,cAAA,WAAA,EAAA;AADA,aALA;AAQA,YAAA,EAAA,EAAA;AACA,cAAA,KAAA,EAAA,iBAAA;AACA,gBAAA,KAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA;AACA;AAHA;AARA,WAAA,EAaA,IAbA,CADA;AAeA,cAAA,MAAA,GAAA,CAAA,CAAA,QAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,IAAA,EAAA;AAFA,aADA;AAKA,YAAA,EAAA,EAAA;AACA,cAAA,KAAA,EAAA,iBAAA;AACA,gBAAA,KAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA;AACA;AAHA;AALA,WAAA,EAWA,IAXA,CAAA;AAYA,cAAA,MAAA,GAAA,CAAA,IAAA,CAAA;;AACA,cAAA,CAAA,KAAA,CAAA,YAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,MAAA;AACA;;AACA,iBAAA,CAAA,CAAA,KAAA,EAAA,MAAA,CAAA;AACA;AAvCA,OAhBA,CAtBA;AAgFA,MAAA,KAAA,EAAA;AACA,QAAA,MAAA,EAAA,IADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,YAAA,EAAA,MAHA;AAIA,QAAA,QAAA,EAAA;AAJA;AAhFA,KAAA;AAuFA,GAnGA;AAoGA,EAAA,OAAA,EAAA;AACA,IAAA,MADA,kBACA,IADA,EACA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,MAAA,GAAA,IAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,KAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,UAAA,IAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EACA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AACA,OAHA,EAGA,GAHA,CAAA;AAIA,KAXA;AAYA,IAAA,MAZA,oBAYA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,KAdA;AAcA,IAAA,GAdA,iBAcA;AACA,WAAA,OAAA,GAAA,IAAA;AACA,WAAA,MAAA,CAAA,QAAA,GAAA,EAAA;AACA,KAjBA;AAkBA,IAAA,MAlBA,kBAkBA,GAlBA,EAkBA;AAAA;;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,OAAA,EAAA,eAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,UAAA,KAAA,CAAA,OAAA,CAAA;AACA,YAAA,GAAA,EAAA,4BADA;AAEA,YAAA,MAAA,EAAA,MAFA;AAGA,YAAA,MAAA,EAAA;AAAA,cAAA,GAAA,EAAA,GAAA,CAAA;AAAA;AAHA,WAAA,EAIA,IAJA,CAIA,YAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,UAAA,CAAA,KAAA;AACA,WANA;AAOA,SAXA;AAYA,QAAA,QAAA,EAAA,oBAAA,CACA;AAbA,OAAA;AAeA,KAlCA;AAmCA,IAAA,MAnCA,oBAmCA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,KAAA;AACA,KArCA;AAsCA,IAAA,MAtCA,oBAsCA;AAAA;;AACA,UAAA,CAAA,KAAA,MAAA,CAAA,QAAA,CAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA;AACA;;AACA,MAAA,KAAA,CAAA,OAAA,CAAA;AACA,QAAA,GAAA,EAAA,qCADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAA,KAAA,EAAA,KAAA,MAAA,CAAA,QAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA;;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,UAAA,CAAA,KAAA;AACA,OARA;AASA,KArDA;AAsDA,IAAA,QAtDA,oBAsDA,GAtDA,EAsDA;AACA,MAAA,KAAA,CAAA,IAAA,CAAA;AACA,QAAA,GAAA,EAAA,8BADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AACA,UAAA,EAAA,EAAA,GAAA,CAAA,EADA;AAEA,UAAA,QAAA,EAAA,GAAA,CAAA,QAFA;AAGA,UAAA,SAAA,EAAA;AAHA;AAHA,OAAA,EAQA,IARA,CAQA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,GAAA,GAAA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AACA,YAAA,QAAA,GAAA,GAAA,CAAA,QAAA;;AACA,YAAA,cAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA,EAAA;AAAA;AACA,cAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,UAAA,KAAA,CAAA,IAAA,GAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,UAAA,KAAA,CAAA,KAAA;AACA,UAAA,GAAA,CAAA,eAAA,CAAA,KAAA,CAAA,IAAA,EAPA,CAOA;;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,SATA,MASA;AAAA;AACA,UAAA,SAAA,CAAA,UAAA,CAAA,IAAA,EAAA,QAAA;AACA;AACA,OAxBA;AAyBA,KAhFA;AAgFA,IAAA,OAhFA,mBAgFA,GAhFA,EAgFA;AAAA;;AACA,UAAA,GAAA,CAAA,SAAA,EAAA;AACA,aAAA,MAAA,GAAA,GAAA,CAAA,SAAA;AACA,OAFA,CAGA;AAHA,WAIA,IAAA,GAAA,CAAA,UAAA,EAAA;AACA,cAAA,OAAA,GAAA,MAAA,CAAA,QAAA,CAAA,MAAA,CADA,CAEA;;AACA,cAAA,UAAA,aAAA,OAAA,cAAA,GAAA,CAAA,UAAA,cAAA,GAAA,CAAA,UAAA,CAAA;AACA,UAAA,GAAA,CAAA,SAAA,GAAA,UAAA;AACA,eAAA,MAAA,GAAA,UAAA;AAEA,cAAA,KAAA,GAAA,IAAA,KAAA,EAAA;AACA,UAAA,KAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CARA,CAQA;;AACA,UAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA,YAAA,GAAA,CAAA,SAAA,GAAA,IAAA;;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA;;AACA,YAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,WAJA;;AAKA,UAAA,KAAA,CAAA,MAAA,GAAA,YAAA;AAAA;AACA,YAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,WAFA;AAGA,SAjBA,MAkBA;AACA,cAAA,GAAA,GAAA,GAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA,cAAA,GAAA,GAAA,GAAA,CAAA,aAAA,GAAA,GAAA,GAAA,GAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,eAAA,OAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,OAAA,CAAA;AACA,YAAA,GAAA,EAAA,+BAAA,GADA;AAEA,YAAA,MAAA,EAAA;AAFA,WAAA,EAGA,IAHA,CAGA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA,YAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA,gBAAA,KAAA,GAAA,IAAA,KAAA,EAAA;AACA,YAAA,KAAA,CAAA,GAAA,GAAA,MAAA,CAAA,MAAA,CAJA,CAIA;;AACA,YAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA,cAAA,GAAA,CAAA,SAAA,GAAA,IAAA;;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA;;AACA,cAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,aAJA;;AAKA,YAAA,KAAA,CAAA,MAAA,GAAA,YAAA;AAAA;AACA,cAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,aAFA,CAVA,CAaA;;AACA,WAjBA;AAkBA;AACA;AA9HA,GApGA;AAmOA,EAAA,OAnOA,qBAmOA;AACA,QAAA,IAAA,GAAA,IAAA;;AACA,IAAA,MAAA,CAAA,QAAA,GAAA,YAAA;AACA,aAAA,YAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,QAAA,CAAA,IAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,MAAA,CAAA,WAAA,GAAA,GAAA;AACA,OAHA,EAAA;AAIA,KALA;;AAMA,IAAA,MAAA,CAAA,QAAA;AACA;AA5OA,CAAA", "sourcesContent": ["<template>\r\n    <div class=\"cl-theme cl-text-list\">\r\n        <Modal v-model=\"show\" :title=\"title\" width=\"90%\" :styles=\"{top: '20px'}\">\r\n            <div>\r\n                <cl-table ref=\"attachList\" :searchable=\"false\" :columns=\"columns\" url=\"/common/attachments/list\"\r\n                          :query-params=\"param\" disable-query-on-mounted method=\"post\" :loading=\"loading\">\r\n                    <div slot=\"buttons\">\r\n                        <Button type=\"primary\" class=\"function\" @click=\"add\" v-if=\"showadd\">添加附件</Button>\r\n                    </div>\r\n                </cl-table>\r\n                <Modal ref=\"attach\" v-model=\"display\" @on-ok=\"upload\" loading>\r\n                    <cl-form v-model=\"attach.fileForm\" :layout=\"attach.formLayout\"></cl-form>\r\n                </Modal>\r\n                <div style=\"text-align: center\">\r\n                    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n                    <img :src=\"imgUrl\" style=\"width: 100%\">\r\n                </div>\r\n            </div>\r\n            <div slot=\"footer\">\r\n                <Button type=\"default\" @click=\"cancle()\">关闭</Button>\r\n            </div>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import axios from '@/libs/api.request'\r\n\r\n    export default {\r\n        name: \"uploadFileModal\",\r\n        components: {},\r\n        props: {\r\n            format: {\r\n                type: Array,\r\n                default: () => {\r\n                    return ['xls', 'xlsx', 'png', 'jpg', 'doc', 'docx', 'mp4', 'gif', 'rar', 'zip','pdf']\r\n                }\r\n            }\r\n        },\r\n        data() {\r\n            return {\r\n                imgUrl: null,\r\n                screenWidth: 800,\r\n                show: false,\r\n                showadd: true,\r\n                display: false,\r\n                loading: false,\r\n                title: '上传图片(点击图片名字查看附件)',\r\n                attach: {\r\n                    fileForm: {\r\n                        file: null\r\n                    },\r\n                    formLayout: [\r\n                        {\r\n                            label: '上传图片',\r\n                            prop: 'file',\r\n                            formItemType: 'file',\r\n                            width: 300,\r\n                            format: this.format\r\n                        }\r\n                    ],\r\n                },\r\n                columns: [\r\n                    {\r\n                        key: 'fileName', title: '文件名',\r\n                        render: (h, params) => {\r\n                            return h('a', {\r\n                                on: {\r\n                                    click: () => {\r\n                                        this.showPic(params.row)\r\n                                    }\r\n                                }\r\n                            }, params.row.fileName)\r\n                        }\r\n                    },\r\n                    {key: 'creatorName', title: '上传人'},\r\n                    {key: 'createTime', title: '上传日期'},\r\n                    {key: 'fileSize', title: '文件大小(KB)'},\r\n                    {\r\n                        title: '操作',\r\n                        key: 'action',\r\n                        width: 200,\r\n                        align: 'center',\r\n                        fixed: 'right',\r\n                        render: (h, params) => {\r\n                            let down =\r\n                                h('Button', {\r\n                                    props: {\r\n                                        type: 'primary',\r\n                                        size: 'small'\r\n                                    },\r\n                                    style: {\r\n                                        marginRight: '3px'\r\n                                    },\r\n                                    on: {\r\n                                        click: () => {\r\n                                            this.download(params.row)\r\n                                        }\r\n                                    }\r\n                                }, '下载')\r\n                            let remove = h('Button', {\r\n                                    props: {\r\n                                        type: 'error',\r\n                                        size: 'small'\r\n                                    },\r\n                                    on: {\r\n                                        click: () => {\r\n                                            this.remove(params.row)\r\n                                        }\r\n                                    }\r\n                                }\r\n                                , '删除')\r\n                            let action = [down]\r\n                            if (!this.downloadOnly) {\r\n                                action.push(remove)\r\n                            }\r\n                            return h('div', action)\r\n                        }\r\n                    }\r\n                ],\r\n                param: {\r\n                    busiId: null,\r\n                    busiAlias: \"附件(台账)\",\r\n                    categoryCode: \"file\",\r\n                    areaCode: \"sc\"\r\n                }\r\n            }\r\n        },\r\n        methods: {\r\n            choose(pcid) {\r\n                this.imgUrl = null;\r\n                this.show = true\r\n                this.param.busiId = pcid + \"\";\r\n                this.$refs.attachList.query();\r\n                let that = this;\r\n                setTimeout(function () {\r\n                    if (that.$refs.attachList.insideData && that.$refs.attachList.insideData.length > 0)\r\n                        that.showPic(that.$refs.attachList.insideData[0])\r\n                }, 200);\r\n            },\r\n            cancle() {\r\n                this.show = false\r\n            }, add() {\r\n                this.display = true\r\n                this.attach.fileForm = {}\r\n            },\r\n            remove(row) {\r\n                this.$Modal.confirm({\r\n                    title: \"确认删除\",\r\n                    content: \"<p>确定删除吗？</p>\",\r\n                    onOk: () => {\r\n                        axios.request({\r\n                            url: '/common/attachments/remove',\r\n                            method: 'post',\r\n                            params: {ids: row.id}\r\n                        }).then(() => {\r\n                            this.$refs.attachList.query()\r\n                        })\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                })\r\n            },\r\n            reload() {\r\n                this.$refs.attachList.query()\r\n            },\r\n            upload() {\r\n                if (!this.attach.fileForm.file) {\r\n                    this.$Message.info({content: '请选择要上传的文件！'})\r\n                    this.$refs.attach.buttonLoading = false\r\n                    return\r\n                }\r\n                axios.request({\r\n                    url: '/common/attachments/uploadMultiFile',\r\n                    method: 'post',\r\n                    data: Object.assign({}, this.param, this.attach.fileForm)\r\n                }).then(() => {\r\n                    this.$refs.attach.buttonLoading = false\r\n                    this.display = false\r\n                    this.$refs.attachList.query()\r\n                })\r\n            },\r\n            download(row) {\r\n                axios.file({\r\n                    url: '/common/attachments/download',\r\n                    method: 'get',\r\n                    params: {\r\n                        id: row.id,\r\n                        shardKey: row.shardKey,\r\n                        thumbnail: ''\r\n                    }\r\n                }).then((res) => {\r\n                    const content = res\r\n                    const blob = new Blob([content])\r\n                    const fileName = row.fileName\r\n                    if ('download' in document.createElement('a')) { // 非IE下载\r\n                        const elink = document.createElement('a')\r\n                        elink.download = fileName\r\n                        elink.style.display = 'none'\r\n                        elink.href = URL.createObjectURL(blob)\r\n                        document.body.appendChild(elink)\r\n                        elink.click()\r\n                        URL.revokeObjectURL(elink.href) // 释放URL 对象\r\n                        document.body.removeChild(elink)\r\n                    } else { // IE10+下载\r\n                        navigator.msSaveBlob(blob, fileName)\r\n                    }\r\n                })\r\n            }, showPic(row) {\r\n                if (row.litimgUrl) {\r\n                    this.imgUrl = row.litimgUrl;\r\n                }\r\n                // 兼容minio迁移，直接构建预览URL\r\n                else if (row.objectName){\r\n                  const baseUrl = window.location.origin;\r\n                  // 直接构建预览URL：baseUrl + / + bucketName + / + objectName\r\n                  const previewUrl = `${baseUrl}/${row.bucketName}/${row.objectName}`;\r\n                  row.litimgUrl = previewUrl;\r\n                  this.imgUrl = previewUrl;\r\n\r\n                  let bgImg = new Image()\r\n                  bgImg.src = this.imgUrl // 获取背景图片的url\r\n                  bgImg.onerror = () => {\r\n                    row.litimgUrl = null;\r\n                    this.$Message.error(\"图片加载失败！！！\");\r\n                    this.loading = false;\r\n                  }\r\n                  bgImg.onload = () => { // 等背景图片加载成功后 去除loading\r\n                    this.loading = false;\r\n                  }\r\n                }\r\n                else {\r\n                    let sub = row.fileName.split(\".\");\r\n                    let key = row.mongodbFileId + \".\" + sub[sub.length - 1];\r\n                    this.loading = true;\r\n                    axios.request({\r\n                        url: '/common/attachments/share/' + key,\r\n                        method: 'get',\r\n                    }).then((res) => {\r\n                        row.litimgUrl = res.data.url;\r\n                        this.imgUrl = res.data.url;\r\n                        let bgImg = new Image()\r\n                        bgImg.src = this.imgUrl // 获取背景图片的url\r\n                        bgImg.onerror = () => {\r\n                            row.litimgUrl = null;\r\n                            this.$Message.error(\"图片加载失败！！！\");\r\n                            this.loading = false;\r\n                        }\r\n                        bgImg.onload = () => { // 等背景图片加载成功后 去除loading\r\n                            this.loading = false;\r\n                        }\r\n                        // this.imgUrl = \"http://objects.objs.paas.sc.ctc.com/s/28aa0721445452647828ccd2e3a0602a.jpg?token=65794a68624763694f694a49557a49314e694973496e523563434936496b705856434a392e65794a6964574e725a5851694f694a7a5932356f4c575a70624755694c434a6c654841694f6a45314e6a67334d4445334e6a5173496d3969616d566a64434936496a4934595745774e7a49784e4451314e4455794e6a51334f44493459324e6b4d6d557a595441324d444a684c6d70775a794a392e625765664946333557794e723236554d3239394b314b6c634f6763646b516f54356f72557238484f794e6b\";\r\n                    })\r\n                }\r\n            }\r\n        }, mounted() {\r\n            const that = this;\r\n            window.onresize = () => {\r\n                return (() => {\r\n                    window.screenWidth = document.body.clientWidth;\r\n                    that.screenWidth = window.screenWidth * 0.7;\r\n                })();\r\n            };\r\n            window.onresize();\r\n        }\r\n\r\n    }\r\n</script>\r\n\r\n<style scoped></style>\r\n"], "sourceRoot": "src/view/account/oilAccount"}]}