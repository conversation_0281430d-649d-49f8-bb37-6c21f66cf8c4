{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\uploadFileModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\uploadFileModal.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["uploadFileModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "uploadFileModal.vue", "sourceRoot": "src/view/account", "sourcesContent": ["<template>\r\n    <div class=\"cl-theme cl-text-list\">\r\n        <Modal v-model=\"show\" :title=\"title\" width=\"90%\" :styles=\"{top: '20px'}\">\r\n            <div>\r\n                <cl-table ref=\"attachList\" :searchable=\"false\" :columns=\"columns\" url=\"/common/attachments/list\"\r\n                          :query-params=\"param\" disable-query-on-mounted method=\"post\" :loading=\"loading\">\r\n                    <div slot=\"buttons\">\r\n                        <Button type=\"primary\" class=\"function\" @click=\"add\" v-if=\"showadd\">添加附件</Button>\r\n                    </div>\r\n                </cl-table>\r\n                <Modal ref=\"attach\" v-model=\"display\" @on-ok=\"upload\" loading>\r\n                    <cl-form v-model=\"attach.fileForm\" :layout=\"attach.formLayout\"></cl-form>\r\n                </Modal>\r\n                <div style=\"text-align: center\">\r\n                    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n                    <img :src=\"imgUrl\" style=\"width: 100%\">\r\n                </div>\r\n            </div>\r\n            <div slot=\"footer\">\r\n                <Button type=\"default\" @click=\"cancle()\">关闭</Button>\r\n            </div>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import axios from '@/libs/api.request'\r\n\r\n    export default {\r\n        name: \"uploadFileModal\",\r\n        components: {},\r\n        props: {\r\n            format: {\r\n                type: Array,\r\n                default: () => {\r\n                    return ['xls', 'xlsx', 'png', 'jpg', 'doc', 'docx', 'mp4', 'gif', 'rar', 'zip','pdf']\r\n                }\r\n            }\r\n        },\r\n        data() {\r\n            return {\r\n                imgUrl: null,\r\n                screenWidth: 800,\r\n                show: false,\r\n                showadd: true,\r\n                display: false,\r\n                loading: false,\r\n                title: '上传图片(点击图片名字查看附件)',\r\n                attach: {\r\n                    fileForm: {\r\n                        file: null\r\n                    },\r\n                    formLayout: [\r\n                        {\r\n                            label: '上传图片',\r\n                            prop: 'file',\r\n                            formItemType: 'file',\r\n                            width: 300,\r\n                            format: this.format\r\n                        }\r\n                    ],\r\n                },\r\n                columns: [\r\n                    {\r\n                        key: 'fileName', title: '文件名',\r\n                        render: (h, params) => {\r\n                            let down =\r\n                                h('Button', {\r\n                                    props: {\r\n                                        type: 'primary',\r\n                                        size: 'small'\r\n                                    },\r\n                                    style: {\r\n                                        marginRight: '3px'\r\n                                    },\r\n                                    on: {\r\n                                        click: () => {\r\n                                            this.download(params.row)\r\n                                        }\r\n                                    }\r\n                                }, '下载')\r\n                            let show = h('a', {\r\n                                on: {\r\n                                    click: () => {\r\n                                        this.showPic(params.row)\r\n                                    }\r\n                                }\r\n                            }, params.row.fileName)\r\n                            let action = [show]\r\n\r\n                             action.push(down)\r\n\r\n                            return h('div', action)\r\n      /*                      return h('a', {\r\n                                on: {\r\n                                    click: () => {\r\n                                        this.showPic(params.row)\r\n                                    }\r\n                                }\r\n                            }, params.row.fileName)*/\r\n                        }\r\n                    },\r\n                    {key: 'creatorName', title: '上传人'},\r\n                    {key: 'createTime', title: '上传日期'},\r\n                    {key: 'fileSize', title: '文件大小(KB)'},\r\n                    {\r\n                        title: '操作',\r\n                        key: 'action',\r\n                        width: 200,\r\n                        align: 'center',\r\n                        fixed: 'right',\r\n                        render: (h, params) => {\r\n                            let down =\r\n                                h('Button', {\r\n                                    props: {\r\n                                        type: 'primary',\r\n                                        size: 'small'\r\n                                    },\r\n                                    style: {\r\n                                        marginRight: '3px'\r\n                                    },\r\n                                    on: {\r\n                                        click: () => {\r\n                                            this.download(params.row)\r\n                                        }\r\n                                    }\r\n                                }, '下载')\r\n                            let remove = h('Button', {\r\n                                    props: {\r\n                                        type: 'error',\r\n                                        size: 'small'\r\n                                    },\r\n                                    on: {\r\n                                        click: () => {\r\n                                            this.remove(params.row)\r\n                                        }\r\n                                    }\r\n                                }\r\n                                , '删除')\r\n                            let action = [down]\r\n                            if (!this.downloadOnly) {\r\n                                action.push(remove)\r\n                            }\r\n                            return h('div', action)\r\n                        }\r\n                    }\r\n                ],\r\n                param: {\r\n                    busiId: null,\r\n                    busiAlias: \"附件(台账)\",\r\n                    categoryCode: \"file\",\r\n                    areaCode: \"sc\"\r\n                }\r\n            }\r\n        },\r\n        methods: {\r\n            choose(pcid) {\r\n                this.imgUrl = null;\r\n                this.show = true\r\n                this.param.busiId = pcid + \"\";\r\n                this.$refs.attachList.query();\r\n                let that = this;\r\n                setTimeout(function () {\r\n                    if (that.$refs.attachList.insideData && that.$refs.attachList.insideData.length > 0)\r\n                        that.showPic(that.$refs.attachList.insideData[0])\r\n                }, 200);\r\n            },\r\n            cancle() {\r\n                this.show = false\r\n            }, add() {\r\n                this.display = true\r\n                this.attach.fileForm = {}\r\n            },\r\n            remove(row) {\r\n                this.$Modal.confirm({\r\n                    title: \"确认删除\",\r\n                    content: \"<p>确定删除吗？</p>\",\r\n                    onOk: () => {\r\n                        axios.request({\r\n                            url: '/common/attachments/remove',\r\n                            method: 'post',\r\n                            params: {ids: row.id}\r\n                        }).then(() => {\r\n                            this.$refs.attachList.query()\r\n                           this.$emit(\"onchange\",1)\r\n                        })\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                })\r\n            },\r\n            reload() {\r\n                this.$refs.attachList.query()\r\n            },\r\n            upload() {\r\n                if (!this.attach.fileForm.file) {\r\n                    this.$Message.info({content: '请选择要上传的文件！'})\r\n                    this.$refs.attach.buttonLoading = false\r\n                    return\r\n                }\r\n                axios.request({\r\n                    url: '/common/attachments/uploadMultiFile',\r\n                    method: 'post',\r\n                    data: Object.assign({}, this.param, this.attach.fileForm)\r\n                }).then(() => {\r\n                    this.$refs.attach.buttonLoading = false\r\n                    this.display = false\r\n                    this.$refs.attachList.query()\r\n                    this.$emit(\"onchange\",1)\r\n                })\r\n            },\r\n            download(row) {\r\n                axios.file({\r\n                    url: '/common/attachments/download',\r\n                    method: 'get',\r\n                    params: {\r\n                        id: row.id,\r\n                        shardKey: row.shardKey,\r\n                        thumbnail: ''\r\n                    }\r\n                }).then((res) => {\r\n                    const content = res\r\n                    const blob = new Blob([content])\r\n                    const fileName = row.fileName\r\n                    if ('download' in document.createElement('a')) { // 非IE下载\r\n                        const elink = document.createElement('a')\r\n                        elink.download = fileName\r\n                        elink.style.display = 'none'\r\n                        elink.href = URL.createObjectURL(blob)\r\n                        document.body.appendChild(elink)\r\n                        elink.click()\r\n                        URL.revokeObjectURL(elink.href) // 释放URL 对象\r\n                        document.body.removeChild(elink)\r\n                    } else { // IE10+下载\r\n                        navigator.msSaveBlob(blob, fileName)\r\n                    }\r\n                })\r\n            }, showPic(row) {\r\n                if (row.litimgUrl) {\r\n                    this.imgUrl = row.litimgUrl;\r\n                }\r\n                // 兼容minio迁移，直接构建预览URL\r\n                else if (row.objectName){\r\n                  const baseUrl = window.location.origin;\r\n                  // 直接构建预览URL：baseUrl + / + bucketName + / + objectName\r\n                  const previewUrl = `${baseUrl}/${row.bucketName}/${row.objectName}`;\r\n                  row.litimgUrl = previewUrl;\r\n                  this.imgUrl = previewUrl;\r\n\r\n                  let bgImg = new Image()\r\n                  bgImg.src = this.imgUrl // 获取背景图片的url\r\n                  bgImg.onerror = () => {\r\n                    row.litimgUrl = null;\r\n                    this.$Message.error(\"图片加载失败！！！\");\r\n                    this.loading = false;\r\n                  }\r\n                  bgImg.onload = () => { // 等背景图片加载成功后 去除loading\r\n                    this.loading = false;\r\n                  }\r\n                }\r\n                else {\r\n                    let sub = row.fileName.split(\".\");\r\n                    let key = row.mongodbFileId + \".\" + sub[sub.length - 1];\r\n                    this.loading = true;\r\n                    axios.request({\r\n                        url: '/common/attachments/share/' + key,\r\n                        method: 'get',\r\n                    }).then((res) => {\r\n                        row.litimgUrl = res.data.url;\r\n                        this.imgUrl = res.data.url;\r\n                        let bgImg = new Image()\r\n                        bgImg.src = this.imgUrl // 获取背景图片的url\r\n                        bgImg.onerror = () => {\r\n                            row.litimgUrl = null;\r\n                            this.$Message.error(\"图片加载失败！！！\");\r\n                            this.loading = false;\r\n                        }\r\n                        bgImg.onload = () => { // 等背景图片加载成功后 去除loading\r\n                            this.loading = false;\r\n                        }\r\n                        // this.imgUrl = \"http://objects.objs.paas.sc.ctc.com/s/28aa0721445452647828ccd2e3a0602a.jpg?token=65794a68624763694f694a49557a49314e694973496e523563434936496b705856434a392e65794a6964574e725a5851694f694a7a5932356f4c575a70624755694c434a6c654841694f6a45314e6a67334d4445334e6a5173496d3969616d566a64434936496a4934595745774e7a49784e4451314e4455794e6a51334f44493459324e6b4d6d557a595441324d444a684c6d70775a794a392e625765664946333557794e723236554d3239394b314b6c634f6763646b516f54356f72557238484f794e6b\";\r\n                    })\r\n                }\r\n            }\r\n        }, mounted() {\r\n            const that = this;\r\n            window.onresize = () => {\r\n                return (() => {\r\n                    window.screenWidth = document.body.clientWidth;\r\n                    that.screenWidth = window.screenWidth * 0.7;\r\n                })();\r\n            };\r\n            window.onresize();\r\n        }\r\n\r\n    }\r\n</script>\r\n\r\n<style scoped></style>\r\n"]}]}