package com.sccl.modules.business.toweraccount.controller;

import com.alibaba.fastjson.JSONObject;
import com.sccl.common.support.Convert;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.aspectj.lang.annotation.Log;
import com.sccl.framework.aspectj.lang.constant.BusinessType;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.framework.web.page.TableDataInfo;
import com.sccl.modules.business.toweraccount.domain.TowerData;
import com.sccl.modules.business.toweraccount.domain.mssbusi;
import com.sccl.modules.business.toweraccount.service.IToweraccountService;
import com.sccl.modules.system.attachments.mapper.AttachmentsMapper;
import com.sccl.modules.system.organization.service.IOrganizationService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 铁塔接口 信息操作处理
 *
 * <AUTHOR>
 * @date 2021-08-12
 */
@RestController
@RequestMapping("/business/toweraccount")
public class ToweraccountController extends BaseController {
    private String prefix = "business/toweraccount";
    @Autowired
    private IOrganizationService organizationService;
    @Autowired
    private IToweraccountService toweraccountService;
    @Autowired(required = false)
    private AttachmentsMapper attachmentsMapper;

    @RequiresPermissions("business:toweraccount:view")
    @GetMapping()
    public String toweraccount() {
        return prefix + "/toweraccount";
    }


    @Value("${scclTnterface.httpforwardUrl}")
    private String httpforwardUrl;

    /**
     * 查询铁塔接口列表
     */
    //@RequiresPermissions("business:towerData:list")
    @RequestMapping("/list")
    @ResponseBody
    public TableDataInfo list(TowerData towerdata) {

        startPage();
        List<TowerData> list = toweraccountService.selectList(towerdata);
        return getDataTable(list);
    }

    /**
     * 新增铁塔接口
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存铁塔接口
     */
    @RequiresPermissions("business:towerData:add")
    @Log(title = "铁塔接口", action = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody TowerData towerData) {
        return toAjax(toweraccountService.insert(towerData));
    }

    /**
     * 修改铁塔接口
     */
    @GetMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id) {
        TowerData towerData = toweraccountService.get(id);

        Object object = JSONObject.toJSON(towerData);

        return this.success(object);
    }

    /**
     * 修改保存铁塔接口
     */
    @RequiresPermissions("business:towerData:edit")
    @Log(title = "铁塔接口", action = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody TowerData towerData) {
        return toAjax(toweraccountService.update(towerData));
    }

    /**
     * 删除铁塔接口
     */
    @RequiresPermissions("business:toweraccount:remove")
    @Log(title = "铁塔接口", action = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(toweraccountService.deleteByIds(Convert.toStrArray(ids)));
    }


    /**
     * 查看铁塔接口
     */
    @RequiresPermissions("business:toweraccount:view")
    @GetMapping("/view/{id}")
    @ResponseBody
    public AjaxResult view(@PathVariable("id") Long id) {
        TowerData towerData = toweraccountService.get(id);

        Object object = JSONObject.toJSON(towerData);

        return this.success(object);
    }

    private void setFilterByRole(TowerData towerData) {
        //业务逻辑，不能放在service中，会影响前端分页
        //根据用户责任中心查询分公司和责任中心
        //默认查询第一个公司和用户的第一个责任中心

        if (towerData.getCompany() == null && towerData.getCountry() == null) {
            towerData.setCompany(-1L);
            towerData.setCountry(-1L);
        } else if (null != towerData.getCompany() && towerData.getCountry() == -1L) {
            towerData.setCompany(towerData.getCompany());
            towerData.setCountry(null);
        }

    }

    /**
     * 加入台账
     */
    //@RequiresPermissions("business:toweraccount:addtaaccount")
    @PostMapping("/addtaaccount")
    @ResponseBody
    public AjaxResult addtaaccount(String ids) {
        int i = 0;
        i = toweraccountService.addaccount(ids);
/*		try {
			List<TowerData> tdlist = toweraccountService.selectListByIds(Convert.toStrArray(ids));
			//List<MssAccountbill> mssAccountbills = mssAccountbillService.selectListByIds(ConvertUtil.toStrArray(ids));
			System.out.println("tdlist size "+tdlist.size());
			for (TowerData towerData : tdlist) {
				if (towerData != null && (towerData.getCompareid()!=null ))
				{
					Account account = new Account();
					account.setAmmeterid(towerData.getCompareid());
					Map<String, Object> map =toweraccountService.selectlst(account);
					if (map != null)
					{	SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
						SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
					if (!StringUtils.isBlank(map.get("enddate").toString())){
						try {
							Date lstenddate = sdf.parse(map.get("enddate").toString());
							Calendar cal = Calendar.getInstance();
							cal.setTime(lstenddate);//设置起时间
							cal.add(Calendar.DATE, 1);//增加一天
							lstenddate=cal.getTime();
							Date tastartdate = sdf1.parse(towerData.getUseStartTime());
							String rtl="";
							if (lstenddate.compareTo(tastartdate)==0)//上期截至时间+1比较铁塔开始时间
							{   BigDecimal startdegree=new BigDecimal(towerData.getUseStartDegree());
								BigDecimal curtotalreadings=new BigDecimal(map.get("curtotalreadings").toString());
								if (startdegree.compareTo(curtotalreadings)==0)//上期截至度数=铁塔开始度数
								{   Account taaccount=new Account();
									taaccount.setAmmeterid(towerData.getCompareid());
									taaccount.setToweraccountid(towerData.getId());
									taaccount.setOrgid(Long.valueOf(map.get("country").toString()));
									taaccount.setStatus(1);
									taaccount.setEffective(new BigDecimal("1"));
									taaccount.setCompany(Long.valueOf(map.get("company").toString()));
									taaccount.setCountry(Long.valueOf(map.get("country").toString()));
				*//*					Date format2 = null;
									format2 = new SimpleDateFormat("yyyy-MM-dd").parse(towerData.getUseStartTime());
									String startshortDate = new SimpleDateFormat("yyyyMMdd").format(format2);
									Date format3 = null;
									format3 = new SimpleDateFormat("yyyy-MM-dd").parse(towerData.getUseEndTime());
									String endshortDate = new SimpleDateFormat("yyyyMMdd").format(format3);*//*
									DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

									String startshortDate = dateFormat.format(convertDate(towerData.getUseStartTime(), "yyyy/MM/dd", "yyyy-MM-dd", "yyyy.MM.dd"));
									String endshortDate=dateFormat.format(convertDate(towerData.getUseEndTime(), "yyyy/MM/dd", "yyyy-MM-dd", "yyyy.MM.dd"));
									taaccount.setStartdate(startshortDate);
									taaccount.setEnddate(endshortDate);
									taaccount.setCurtotalreadings(new BigDecimal(towerData.getUseEndDegree()));
									taaccount.setPrevtotalreadings(new BigDecimal(towerData.getUseStartDegree()));
									taaccount.setCurusedreadings(new BigDecimal(towerData.getEnergyUsed()));
									taaccount.setMulttimes(new BigDecimal(towerData.getMagnification()));
									taaccount.setTotalusedreadings(new BigDecimal(towerData.getEnergyUsed()).multiply(new BigDecimal(towerData.getApportionmentRatio())));
									taaccount.setPercent(new BigDecimal(towerData.getApportionmentRatio()).multiply(new BigDecimal(100)));
									taaccount.setAccountmoney(new BigDecimal(towerData.getTelecomBillingAmount()).setScale(2,BigDecimal.ROUND_HALF_UP));

									taaccount.setTaxticketmoney(new BigDecimal(towerData.getTelecomBillingAmount()).setScale(2,BigDecimal.ROUND_HALF_UP));
									System.out.println("ActualPay:"+new BigDecimal(towerData.getActualPay()));
									//taaccount.setInputtaxticketmoney(new BigDecimal(towerData.getActualPay()));
									taaccount.setInputtaxticketmoney(new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(towerData.getApportionmentRatio()),2, BigDecimal.ROUND_HALF_UP));
									System.out.println(new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(1.13),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(0.13)).setScale(2,BigDecimal.ROUND_HALF_UP));
									BigDecimal tax=new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(1.13),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(0.13)).setScale(2,BigDecimal.ROUND_HALF_UP);
									System.out.println("tax:"+tax);
									taaccount.setTaxamount(tax);
									taaccount.setTaxrate(new BigDecimal(13).setScale(2,BigDecimal.ROUND_HALF_UP));
									System.out.println("ApportionmentRatio: "+towerData.getApportionmentRatio().toString());
									System.out.println("ApportionmentRatio: "+towerData.getApportionmentRatio().toString());
									BigDecimal uprice=new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(towerData.getEnergyUsed()).multiply(new BigDecimal(towerData.getApportionmentRatio())),2,BigDecimal.ROUND_HALF_UP);
									taaccount.setUnitpirce(uprice);
									taaccount.setInputdate(now());
									taaccount.setLasteditdate(now());
									taaccount.setRru(new BigDecimal(0));
									taaccount.setIsnew(new BigDecimal(0));
									taaccount.setOpflag(new BigDecimal(0));
									System.out.println(towerData.getAnnex());

									System.out.println(towerData.getPowerLoss());
									if (towerData.getPowerLoss()==null)
										taaccount.setTransformerullage(new BigDecimal(0));
										else
									taaccount.setTransformerullage(new BigDecimal(towerData.getPowerLoss()).multiply(new BigDecimal(towerData.getApportionmentRatio())));
									String accountnostr=map.get("accountno").toString()+"01";
									Date formatacc = null;
									formatacc = new SimpleDateFormat("yyyyMMdd").parse(accountnostr);
									Calendar calacc = Calendar.getInstance();
									calacc.setTime(formatacc);//设置起时间
									calacc.add(Calendar.MONTH, 1);//增加一月
									Date accountnodate=calacc.getTime();
									String yyyyMM = FastDateFormat.getInstance("yyyyMM").format(accountnodate);
									taaccount.setAccountno(yyyyMM);
									toweraccountService.deloldaccount(taaccount);
									toweraccountService.inserttaaccount(taaccount);
									if (towerData.getAnnex()!=null)
									{
										Attachments at =new Attachments();
										at.setBusiId(towerData.getId());
										toweraccountService.upannix(at,taaccount.getPcid());
									}

					*//*				{rtl=uploadaccount(towerData.getAnnex(),taaccount.getPcid());
										System.out.println("rtl:"+rtl);}*//*
								}
						    }
						} catch (Exception e) {
							System.out.println(e.getMessage());
							throw e;
						}
						}
					}

						//System.out.println("nh percent "+map.get("percent").toString()+"ApportionmentRatio "+towerData.getApportionmentRatio());
						//System.out.println("TowerSiteCode:"+towerData.getTowerSiteCode()+" ,UseEndTime:"+towerData.getUseEndTime()+"nh lst end date"+map.get("startdate").toString() +" ,getUseStartTime:"+towerData.getUseStartTime()+"nh lst enddate"+map.get("enddate").toString() + " ,UseEndTime:"+towerData.getUseEndTime()+" ,UseStartDegree:"+towerData.getUseStartDegree() +"nh lst prevtotalreadings"+map.get("prevtotalreadings").toString()+towerData.getUseEndDegree() +"nh lst prevtotalreadings"+map.get("curtotalreadings").toString());
				}
				i++;
			}

//            i = mssAccountbillService.deleteByIdsAuto(ConvertFormat.toStrArray(ids));
			return this.success("加入(" + i + ")条");
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println(e.getMessage());
			return this.error(1, "加入台账失败:" + e.getMessage());
		}*/
        return this.success("加入(" + i + ")条");
    }

    private String uploadaccount(String files, Long pcid) {
        Map<String, Object> param = new HashMap<>();
        param.put("files", files);
        param.put("pcid", pcid);
        RestTemplate rest = new RestTemplate();
        rest.getMessageConverters().add(new StringHttpMessageConverter(Charset.forName("UTF-8")));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(param, headers);
        System.out.println("httpforwardUrl doMss=" + httpforwardUrl);
        return rest.postForObject(httpforwardUrl + "getTafiles", requestEntity, String.class);

    }

    /**
     * 加入台账
     */
    //@RequiresPermissions("business:toweraccount:addtaaccount")
    @PostMapping("/batchaccount")
    @ResponseBody
    public AjaxResult batchaccount(String orgid) {
        int i = 0;
        i = toweraccountService.batchaccount(orgid);
		/*try {
			List<TowerData> tdlist = toweraccountService.selectListByOrgid(orgid);
			//List<MssAccountbill> mssAccountbills = mssAccountbillService.selectListByIds(Convert.toStrArray(ids));
			for (TowerData towerData : tdlist) {
				if (towerData != null && (towerData.getCompareid()!=null ))
				{
					Account account = new Account();
					account.setAmmeterid(towerData.getCompareid());
					Map<String, Object> map =toweraccountService.selectlst(account);
					if (map != null)
					{	SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
						SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
						if (!StringUtils.isBlank(map.get("enddate").toString())){
							try {
								Date lstenddate = sdf.parse(map.get("enddate").toString());
								Calendar cal = Calendar.getInstance();
								cal.setTime(lstenddate);//设置起时间
								cal.add(Calendar.DATE, 1);//增加一天
								lstenddate=cal.getTime();
								Date tastartdate = sdf1.parse(towerData.getUseStartTime());
								String rtl="";
								if (lstenddate.compareTo(tastartdate)==0)//上期截至时间+1比较铁塔开始时间
								{   BigDecimal startdegree=new BigDecimal(towerData.getUseStartDegree());
									BigDecimal curtotalreadings=new BigDecimal(map.get("curtotalreadings").toString());
									if (startdegree.compareTo(curtotalreadings)==0)//上期截至度数=铁塔开始度数
									{   Account taaccount=new Account();
										taaccount.setAmmeterid(towerData.getCompareid());
										taaccount.setToweraccountid(towerData.getId());
										taaccount.setOrgid(Long.valueOf(map.get("country").toString()));
										taaccount.setStatus(1);
										taaccount.setEffective(new BigDecimal("1"));
										taaccount.setCompany(Long.valueOf(map.get("company").toString()));
										taaccount.setCountry(Long.valueOf(map.get("country").toString()));
										Date format2 = null;
										format2 = new SimpleDateFormat("yyyy-MM-dd").parse(towerData.getUseStartTime());
										String startshortDate = new SimpleDateFormat("yyyyMMdd").format(format2);
										Date format3 = null;
										format3 = new SimpleDateFormat("yyyy-MM-dd").parse(towerData.getUseEndTime());
										String endshortDate = new SimpleDateFormat("yyyyMMdd").format(format3);
										taaccount.setStartdate(startshortDate);
										taaccount.setEnddate(endshortDate);
										taaccount.setCurtotalreadings(new BigDecimal(towerData.getUseEndDegree()));
										taaccount.setPrevtotalreadings(new BigDecimal(towerData.getUseStartDegree()));
										taaccount.setCurusedreadings(new BigDecimal(towerData.getEnergyUsed()));
										taaccount.setMulttimes(new BigDecimal(towerData.getMagnification()));
										taaccount.setTotalusedreadings(new BigDecimal(towerData.getEnergyUsed()).multiply(new BigDecimal(towerData.getApportionmentRatio())));
										taaccount.setPercent(new BigDecimal(towerData.getApportionmentRatio()).multiply(new BigDecimal(100)));
										taaccount.setAccountmoney(new BigDecimal(towerData.getTelecomBillingAmount()).setScale(2,BigDecimal.ROUND_HALF_UP));

										taaccount.setTaxticketmoney(new BigDecimal(towerData.getTelecomBillingAmount()).setScale(2,BigDecimal.ROUND_HALF_UP));
										System.out.println("ActualPay:"+new BigDecimal(towerData.getActualPay()));
										//taaccount.setInputtaxticketmoney(new BigDecimal(towerData.getActualPay()));
										taaccount.setInputtaxticketmoney(new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(towerData.getApportionmentRatio()),2, BigDecimal.ROUND_HALF_UP));
										System.out.println(new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(1.13),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(0.13)).setScale(2,BigDecimal.ROUND_HALF_UP));
										BigDecimal tax=new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(1.13),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(0.13)).setScale(2,BigDecimal.ROUND_HALF_UP);
										System.out.println("tax:"+tax);
										taaccount.setTaxamount(tax);
										System.out.println("tax:"+tax);
										taaccount.setTaxrate(new BigDecimal(13).setScale(2,BigDecimal.ROUND_HALF_UP));
										BigDecimal uprice=new BigDecimal(towerData.getTelecomBillingAmount()).divide(new BigDecimal(towerData.getEnergyUsed()).multiply(new BigDecimal(towerData.getApportionmentRatio())),2,BigDecimal.ROUND_HALF_UP);
										taaccount.setUnitpirce(uprice);
										taaccount.setInputdate(now());
										taaccount.setLasteditdate(now());
										taaccount.setRru(new BigDecimal(0));
										taaccount.setIsnew(new BigDecimal(0));
										taaccount.setOpflag(new BigDecimal(0));
										System.out.println(towerData.getAnnex());

										System.out.println(towerData.getPowerLoss());
										if (towerData.getPowerLoss()==null)
											taaccount.setTransformerullage(new BigDecimal(0));
										else
											taaccount.setTransformerullage(new BigDecimal(towerData.getPowerLoss()).multiply(new BigDecimal(towerData.getApportionmentRatio())));
										String accountnostr=map.get("accountno").toString()+"01";
										Date formatacc = null;
										formatacc = new SimpleDateFormat("yyyyMMdd").parse(accountnostr);
										Calendar calacc = Calendar.getInstance();
										calacc.setTime(formatacc);//设置起时间
										calacc.add(Calendar.MONTH, 1);//增加一月
										Date accountnodate=calacc.getTime();
										String yyyyMM = FastDateFormat.getInstance("yyyyMM").format(accountnodate);
										String yyyy = FastDateFormat.getInstance("yyyyMM").format(accountnodate);
										taaccount.setAccountno(yyyyMM);
										taaccount.setYear(yyyy);
										taaccount.setTaRemark(towerData.getRemark());
										Message msg=selectLstMsg(towerData.getId());
										if (msg!=null&&msg.getRongStep()==9)
											taaccount.setRemark(msg.getMessage());
										toweraccountService.deloldaccount(taaccount);
										toweraccountService.inserttaaccount(taaccount);
										if (towerData.getAnnex()!=null)
										{//rtl=uploadaccount(towerData.getAnnex(),taaccount.getPcid());
											Attachments at =new Attachments();
											at.setBusiId(towerData.getId());
											toweraccountService.upannix(at,taaccount.getPcid());
											System.out.println("rtl:"+rtl);}
									}
								}
							} catch (Exception e) {
								System.out.println(e.getMessage());
								throw e;
							}
						}
					}

					//System.out.println("nh percent "+map.get("percent").toString()+"ApportionmentRatio "+towerData.getApportionmentRatio());
					//System.out.println("TowerSiteCode:"+towerData.getTowerSiteCode()+" ,UseEndTime:"+towerData.getUseEndTime()+"nh lst end date"+map.get("startdate").toString() +" ,getUseStartTime:"+towerData.getUseStartTime()+"nh lst enddate"+map.get("enddate").toString() + " ,UseEndTime:"+towerData.getUseEndTime()+" ,UseStartDegree:"+towerData.getUseStartDegree() +"nh lst prevtotalreadings"+map.get("prevtotalreadings").toString()+towerData.getUseEndDegree() +"nh lst prevtotalreadings"+map.get("curtotalreadings").toString());
				}
				i++;
			}

//            i = mssAccountbillService.deleteByIdsAuto(ConvertFormat.toStrArray(ids));
			return this.success("加入(" + i + ")条");
 		} catch (Exception e) {
			e.printStackTrace();
			return this.error(1, "加入台账失败:" + e.getMessage());
		}*/
        return this.success("加入(" + i + ")条");
    }

    /**
     * 查询业财一致列表
     */

    @RequestMapping("/listmssbusi")
    @ResponseBody
    public TableDataInfo listmssbusi(
            @RequestParam(value = "company", required = false) String company,
            @RequestParam(value = "country", required = false) String country,
            @RequestParam(value = "startDate", required = false) String startDate) {

        TowerData towerData = new TowerData();
        towerData.setCity("-1".equals(company) ? null : company);
        towerData.setCountry("-1".equals(country) ? null : Long.valueOf(country));
        towerData.setUpTime(startDate);

        startPage();
        List<mssbusi> list = toweraccountService.seletmssbusi(towerData);
        return getDataTable(list);
    }
}
