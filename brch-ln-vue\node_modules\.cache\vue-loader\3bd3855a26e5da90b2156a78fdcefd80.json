{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\oilAccount\\uploadFileModal.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\oilAccount\\uploadFileModal.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBheGlvcyBmcm9tICdAL2xpYnMvYXBpLnJlcXVlc3QnCgpleHBvcnQgZGVmYXVsdCB7CiAgICBuYW1lOiAidXBsb2FkRmlsZU1vZGFsIiwKICAgIGNvbXBvbmVudHM6IHt9LAogICAgcHJvcHM6IHsKICAgICAgICBmb3JtYXQ6IHsKICAgICAgICAgICAgdHlwZTogQXJyYXksCiAgICAgICAgICAgIGRlZmF1bHQ6ICgpID0+IHsKICAgICAgICAgICAgICAgIHJldHVybiBbJ3hscycsICd4bHN4JywgJ3BuZycsICdqcGcnLCAnZG9jJywgJ2RvY3gnLCAnbXA0JywgJ2dpZicsICdyYXInLCAnemlwJywncGRmJ10KICAgICAgICAgICAgfQogICAgICAgIH0KICAgIH0sCiAgICBkYXRhKCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIGltZ1VybDogbnVsbCwKICAgICAgICAgICAgc2NyZWVuV2lkdGg6IDgwMCwKICAgICAgICAgICAgc2hvdzogZmFsc2UsCiAgICAgICAgICAgIHNob3dhZGQ6IHRydWUsCiAgICAgICAgICAgIGRpc3BsYXk6IGZhbHNlLAogICAgICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAgICAgdGl0bGU6ICfkuIrkvKDlm77niYco54K55Ye75Zu+54mH5ZCN5a2X5p+l55yL6ZmE5Lu2KScsCiAgICAgICAgICAgIGF0dGFjaDogewogICAgICAgICAgICAgICAgZmlsZUZvcm06IHsKICAgICAgICAgICAgICAgICAgICBmaWxlOiBudWxsCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgZm9ybUxheW91dDogWwogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICfkuIrkvKDlm77niYcnLAogICAgICAgICAgICAgICAgICAgICAgICBwcm9wOiAnZmlsZScsCiAgICAgICAgICAgICAgICAgICAgICAgIGZvcm1JdGVtVHlwZTogJ2ZpbGUnLAogICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMzAwLAogICAgICAgICAgICAgICAgICAgICAgICBmb3JtYXQ6IHRoaXMuZm9ybWF0CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgfSwKICAgICAgICAgICAgY29sdW1uczogWwogICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGtleTogJ2ZpbGVOYW1lJywgdGl0bGU6ICfmlofku7blkI0nLAogICAgICAgICAgICAgICAgICAgIHJlbmRlcjogKGgsIHBhcmFtcykgPT4gewogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gaCgnYScsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xpY2s6ICgpID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zaG93UGljKHBhcmFtcy5yb3cpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICB9LCBwYXJhbXMucm93LmZpbGVOYW1lKQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICB7a2V5OiAnY3JlYXRvck5hbWUnLCB0aXRsZTogJ+S4iuS8oOS6uid9LAogICAgICAgICAgICAgICAge2tleTogJ2NyZWF0ZVRpbWUnLCB0aXRsZTogJ+S4iuS8oOaXpeacnyd9LAogICAgICAgICAgICAgICAge2tleTogJ2ZpbGVTaXplJywgdGl0bGU6ICfmlofku7blpKflsI8oS0IpJ30sCiAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfmk43kvZwnLAogICAgICAgICAgICAgICAgICAgIGtleTogJ2FjdGlvbicsCiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDIwMCwKICAgICAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICAgICAgZml4ZWQ6ICdyaWdodCcsCiAgICAgICAgICAgICAgICAgICAgcmVuZGVyOiAoaCwgcGFyYW1zKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBkb3duID0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGgoJ0J1dHRvbicsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9wczogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAncHJpbWFyeScsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU6ICdzbWFsbCcKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpblJpZ2h0OiAnM3B4JwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xpY2s6ICgpID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZG93bmxvYWQocGFyYW1zLnJvdykKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sICfkuIvovb0nKQogICAgICAgICAgICAgICAgICAgICAgICBsZXQgcmVtb3ZlID0gaCgnQnV0dG9uJywgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb3BzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU6ICdzbWFsbCcKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsaWNrOiAoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnJlbW92ZShwYXJhbXMucm93KQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLCAn5Yig6ZmkJykKICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGFjdGlvbiA9IFtkb3duXQogICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuZG93bmxvYWRPbmx5KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3Rpb24ucHVzaChyZW1vdmUpCiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGgoJ2RpdicsIGFjdGlvbikKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIHBhcmFtOiB7CiAgICAgICAgICAgICAgICBidXNpSWQ6IG51bGwsCiAgICAgICAgICAgICAgICBidXNpQWxpYXM6ICLpmYTku7Yo5Y+w6LSmKSIsCiAgICAgICAgICAgICAgICBjYXRlZ29yeUNvZGU6ICJmaWxlIiwKICAgICAgICAgICAgICAgIGFyZWFDb2RlOiAic2MiCiAgICAgICAgICAgIH0KICAgICAgICB9CiAgICB9LAogICAgbWV0aG9kczogewogICAgICAgIGNob29zZShwY2lkKSB7CiAgICAgICAgICAgIHRoaXMuaW1nVXJsID0gbnVsbDsKICAgICAgICAgICAgdGhpcy5zaG93ID0gdHJ1ZQogICAgICAgICAgICB0aGlzLnBhcmFtLmJ1c2lJZCA9IHBjaWQgKyAiIjsKICAgICAgICAgICAgdGhpcy4kcmVmcy5hdHRhY2hMaXN0LnF1ZXJ5KCk7CiAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpczsKICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICBpZiAodGhhdC4kcmVmcy5hdHRhY2hMaXN0Lmluc2lkZURhdGEgJiYgdGhhdC4kcmVmcy5hdHRhY2hMaXN0Lmluc2lkZURhdGEubGVuZ3RoID4gMCkKICAgICAgICAgICAgICAgICAgICB0aGF0LnNob3dQaWModGhhdC4kcmVmcy5hdHRhY2hMaXN0Lmluc2lkZURhdGFbMF0pCiAgICAgICAgICAgIH0sIDIwMCk7CiAgICAgICAgfSwKICAgICAgICBjYW5jbGUoKSB7CiAgICAgICAgICAgIHRoaXMuc2hvdyA9IGZhbHNlCiAgICAgICAgfSwgYWRkKCkgewogICAgICAgICAgICB0aGlzLmRpc3BsYXkgPSB0cnVlCiAgICAgICAgICAgIHRoaXMuYXR0YWNoLmZpbGVGb3JtID0ge30KICAgICAgICB9LAogICAgICAgIHJlbW92ZShyb3cpIHsKICAgICAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7CiAgICAgICAgICAgICAgICB0aXRsZTogIuehruiupOWIoOmZpCIsCiAgICAgICAgICAgICAgICBjb250ZW50OiAiPHA+56Gu5a6a5Yig6Zmk5ZCX77yfPC9wPiIsCiAgICAgICAgICAgICAgICBvbk9rOiAoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgYXhpb3MucmVxdWVzdCh7CiAgICAgICAgICAgICAgICAgICAgICAgIHVybDogJy9jb21tb24vYXR0YWNobWVudHMvcmVtb3ZlJywKICAgICAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgICAgICAgICAgICAgICAgICAgIHBhcmFtczoge2lkczogcm93LmlkfQogICAgICAgICAgICAgICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLmF0dGFjaExpc3QucXVlcnkoKQogICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgb25DYW5jZWw6ICgpID0+IHsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIHJlbG9hZCgpIHsKICAgICAgICAgICAgdGhpcy4kcmVmcy5hdHRhY2hMaXN0LnF1ZXJ5KCkKICAgICAgICB9LAogICAgICAgIHVwbG9hZCgpIHsKICAgICAgICAgICAgaWYgKCF0aGlzLmF0dGFjaC5maWxlRm9ybS5maWxlKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oe2NvbnRlbnQ6ICfor7fpgInmi6nopoHkuIrkvKDnmoTmlofku7bvvIEnfSkKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuYXR0YWNoLmJ1dHRvbkxvYWRpbmcgPSBmYWxzZQogICAgICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICAgIH0KICAgICAgICAgICAgYXhpb3MucmVxdWVzdCh7CiAgICAgICAgICAgICAgICB1cmw6ICcvY29tbW9uL2F0dGFjaG1lbnRzL3VwbG9hZE11bHRpRmlsZScsCiAgICAgICAgICAgICAgICBtZXRob2Q6ICdwb3N0JywKICAgICAgICAgICAgICAgIGRhdGE6IE9iamVjdC5hc3NpZ24oe30sIHRoaXMucGFyYW0sIHRoaXMuYXR0YWNoLmZpbGVGb3JtKQogICAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuYXR0YWNoLmJ1dHRvbkxvYWRpbmcgPSBmYWxzZQogICAgICAgICAgICAgICAgdGhpcy5kaXNwbGF5ID0gZmFsc2UKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuYXR0YWNoTGlzdC5xdWVyeSgpCiAgICAgICAgICAgIH0pCiAgICAgICAgfSwKICAgICAgICBkb3dubG9hZChyb3cpIHsKICAgICAgICAgICAgYXhpb3MuZmlsZSh7CiAgICAgICAgICAgICAgICB1cmw6ICcvY29tbW9uL2F0dGFjaG1lbnRzL2Rvd25sb2FkJywKICAgICAgICAgICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgICAgICAgICAgICBpZDogcm93LmlkLAogICAgICAgICAgICAgICAgICAgIHNoYXJkS2V5OiByb3cuc2hhcmRLZXksCiAgICAgICAgICAgICAgICAgICAgdGh1bWJuYWlsOiAnJwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSByZXMKICAgICAgICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbY29udGVudF0pCiAgICAgICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9IHJvdy5maWxlTmFtZQogICAgICAgICAgICAgICAgaWYgKCdkb3dubG9hZCcgaW4gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpKSB7IC8vIOmdnklF5LiL6L29CiAgICAgICAgICAgICAgICAgICAgY29uc3QgZWxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJykKICAgICAgICAgICAgICAgICAgICBlbGluay5kb3dubG9hZCA9IGZpbGVOYW1lCiAgICAgICAgICAgICAgICAgICAgZWxpbmsuc3R5bGUuZGlzcGxheSA9ICdub25lJwogICAgICAgICAgICAgICAgICAgIGVsaW5rLmhyZWYgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpCiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChlbGluaykKICAgICAgICAgICAgICAgICAgICBlbGluay5jbGljaygpCiAgICAgICAgICAgICAgICAgICAgVVJMLnJldm9rZU9iamVjdFVSTChlbGluay5ocmVmKSAvLyDph4rmlL5VUkwg5a+56LGhCiAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChlbGluaykKICAgICAgICAgICAgICAgIH0gZWxzZSB7IC8vIElFMTAr5LiL6L29CiAgICAgICAgICAgICAgICAgICAgbmF2aWdhdG9yLm1zU2F2ZUJsb2IoYmxvYiwgZmlsZU5hbWUpCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgfSwgc2hvd1BpYyhyb3cpIHsKICAgICAgICAgICAgaWYgKHJvdy5saXRpbWdVcmwpIHsKICAgICAgICAgICAgICAgIHRoaXMuaW1nVXJsID0gcm93LmxpdGltZ1VybDsKICAgICAgICAgICAgfQogICAgICAgICAgICAvLyDlhbzlrrltaW5pb+i/geenu++8jOebtOaOpeaehOW7uumihOiniFVSTAogICAgICAgICAgICBlbHNlIGlmIChyb3cub2JqZWN0TmFtZSl7CiAgICAgICAgICAgICAgY29uc3QgYmFzZVVybCA9IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW47CiAgICAgICAgICAgICAgLy8g55u05o6l5p6E5bu66aKE6KeIVVJM77yaYmFzZVVybCArIC8gKyBidWNrZXROYW1lICsgLyArIG9iamVjdE5hbWUKICAgICAgICAgICAgICBjb25zdCBwcmV2aWV3VXJsID0gYCR7YmFzZVVybH0vJHtyb3cuYnVja2V0TmFtZX0vJHtyb3cub2JqZWN0TmFtZX1gOwogICAgICAgICAgICAgIHJvdy5saXRpbWdVcmwgPSBwcmV2aWV3VXJsOwogICAgICAgICAgICAgIHRoaXMuaW1nVXJsID0gcHJldmlld1VybDsKCiAgICAgICAgICAgICAgbGV0IGJnSW1nID0gbmV3IEltYWdlKCkKICAgICAgICAgICAgICBiZ0ltZy5zcmMgPSB0aGlzLmltZ1VybCAvLyDojrflj5bog4zmma/lm77niYfnmoR1cmwKICAgICAgICAgICAgICBiZ0ltZy5vbmVycm9yID0gKCkgPT4gewogICAgICAgICAgICAgICAgcm93LmxpdGltZ1VybCA9IG51bGw7CiAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCLlm77niYfliqDovb3lpLHotKXvvIHvvIHvvIEiKTsKICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBiZ0ltZy5vbmxvYWQgPSAoKSA9PiB7IC8vIOetieiDjOaZr+WbvueJh+WKoOi9veaIkOWKn+WQjiDljrvpmaRsb2FkaW5nCiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgZWxzZSB7CiAgICAgICAgICAgICAgICBsZXQgc3ViID0gcm93LmZpbGVOYW1lLnNwbGl0KCIuIik7CiAgICAgICAgICAgICAgICBsZXQga2V5ID0gcm93Lm1vbmdvZGJGaWxlSWQgKyAiLiIgKyBzdWJbc3ViLmxlbmd0aCAtIDFdOwogICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICAgIGF4aW9zLnJlcXVlc3QoewogICAgICAgICAgICAgICAgICAgIHVybDogJy9jb21tb24vYXR0YWNobWVudHMvc2hhcmUvJyArIGtleSwKICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgcm93LmxpdGltZ1VybCA9IHJlcy5kYXRhLnVybDsKICAgICAgICAgICAgICAgICAgICB0aGlzLmltZ1VybCA9IHJlcy5kYXRhLnVybDsKICAgICAgICAgICAgICAgICAgICBsZXQgYmdJbWcgPSBuZXcgSW1hZ2UoKQogICAgICAgICAgICAgICAgICAgIGJnSW1nLnNyYyA9IHRoaXMuaW1nVXJsIC8vIOiOt+WPluiDjOaZr+WbvueJh+eahHVybAogICAgICAgICAgICAgICAgICAgIGJnSW1nLm9uZXJyb3IgPSAoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHJvdy5saXRpbWdVcmwgPSBudWxsOwogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCLlm77niYfliqDovb3lpLHotKXvvIHvvIHvvIEiKTsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIGJnSW1nLm9ubG9hZCA9ICgpID0+IHsgLy8g562J6IOM5pmv5Zu+54mH5Yqg6L295oiQ5Yqf5ZCOIOWOu+mZpGxvYWRpbmcKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIC8vIHRoaXMuaW1nVXJsID0gImh0dHA6Ly9vYmplY3RzLm9ianMucGFhcy5zYy5jdGMuY29tL3MvMjhhYTA3MjE0NDU0NTI2NDc4MjhjY2QyZTNhMDYwMmEuanBnP3Rva2VuPTY1Nzk0YTY4NjI0NzYzNjk0ZjY5NGE0OTU1N2E0OTMxNGU2OTQ5NzM0OTZlNTIzNTYzNDM0OTM2NDk2YjcwNTg1NjQzNGEzOTJlNjU3OTRhNjk2NDU3NGU3MjVhNTg1MTY5NGY2OTRhN2E1OTMyMzU2ZjRjNTc1YTcwNjI0NzU1Njk0YzQzNGE2YzY1NDg0MTY5NGY2YTQ1MzE0ZTZhNjczMzRkNDQ0NTMzNGU2YTUxNzM0OTZkMzk2OTYxNmQ1NjZhNjQ0MzQ5MzY0OTZhNDkzNDU5NTc0NTc3NGU3YTQ5Nzg0ZTQ0NTEzMTRlNDQ1NTc5NGU2YTUxMzM0ZjQ0NDkzNDU5MzI0ZTZiNGQ2ZDU1N2E1OTU0NDEzMjRkNDQ0YTY4NGM2ZDcwNzc1YTc5NGEzOTJlNjI1NzY1NjY0OTQ2MzMzNTU3Nzk0ZTcyMzIzNjU1NGQzMjM5Mzk0YjMxNGI2YzYzNGY2NzYzNjQ2YjUxNmY1NDM1NmY3MjU1NzIzODQ4NGY3OTRlNmIiOwogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfQogICAgICAgIH0KICAgIH0sIG1vdW50ZWQoKSB7CiAgICAgICAgY29uc3QgdGhhdCA9IHRoaXM7CiAgICAgICAgd2luZG93Lm9ucmVzaXplID0gKCkgPT4gewogICAgICAgICAgICByZXR1cm4gKCgpID0+IHsKICAgICAgICAgICAgICAgIHdpbmRvdy5zY3JlZW5XaWR0aCA9IGRvY3VtZW50LmJvZHkuY2xpZW50V2lkdGg7CiAgICAgICAgICAgICAgICB0aGF0LnNjcmVlbldpZHRoID0gd2luZG93LnNjcmVlbldpZHRoICogMC43OwogICAgICAgICAgICB9KSgpOwogICAgICAgIH07CiAgICAgICAgd2luZG93Lm9ucmVzaXplKCk7CiAgICB9Cgp9Cg=="}, {"version": 3, "sources": ["uploadFileModal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "uploadFileModal.vue", "sourceRoot": "src/view/account/oilAccount", "sourcesContent": ["<template>\r\n    <div class=\"cl-theme cl-text-list\">\r\n        <Modal v-model=\"show\" :title=\"title\" width=\"90%\" :styles=\"{top: '20px'}\">\r\n            <div>\r\n                <cl-table ref=\"attachList\" :searchable=\"false\" :columns=\"columns\" url=\"/common/attachments/list\"\r\n                          :query-params=\"param\" disable-query-on-mounted method=\"post\" :loading=\"loading\">\r\n                    <div slot=\"buttons\">\r\n                        <Button type=\"primary\" class=\"function\" @click=\"add\" v-if=\"showadd\">添加附件</Button>\r\n                    </div>\r\n                </cl-table>\r\n                <Modal ref=\"attach\" v-model=\"display\" @on-ok=\"upload\" loading>\r\n                    <cl-form v-model=\"attach.fileForm\" :layout=\"attach.formLayout\"></cl-form>\r\n                </Modal>\r\n                <div style=\"text-align: center\">\r\n                    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n                    <img :src=\"imgUrl\" style=\"width: 100%\">\r\n                </div>\r\n            </div>\r\n            <div slot=\"footer\">\r\n                <Button type=\"default\" @click=\"cancle()\">关闭</Button>\r\n            </div>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import axios from '@/libs/api.request'\r\n\r\n    export default {\r\n        name: \"uploadFileModal\",\r\n        components: {},\r\n        props: {\r\n            format: {\r\n                type: Array,\r\n                default: () => {\r\n                    return ['xls', 'xlsx', 'png', 'jpg', 'doc', 'docx', 'mp4', 'gif', 'rar', 'zip','pdf']\r\n                }\r\n            }\r\n        },\r\n        data() {\r\n            return {\r\n                imgUrl: null,\r\n                screenWidth: 800,\r\n                show: false,\r\n                showadd: true,\r\n                display: false,\r\n                loading: false,\r\n                title: '上传图片(点击图片名字查看附件)',\r\n                attach: {\r\n                    fileForm: {\r\n                        file: null\r\n                    },\r\n                    formLayout: [\r\n                        {\r\n                            label: '上传图片',\r\n                            prop: 'file',\r\n                            formItemType: 'file',\r\n                            width: 300,\r\n                            format: this.format\r\n                        }\r\n                    ],\r\n                },\r\n                columns: [\r\n                    {\r\n                        key: 'fileName', title: '文件名',\r\n                        render: (h, params) => {\r\n                            return h('a', {\r\n                                on: {\r\n                                    click: () => {\r\n                                        this.showPic(params.row)\r\n                                    }\r\n                                }\r\n                            }, params.row.fileName)\r\n                        }\r\n                    },\r\n                    {key: 'creatorName', title: '上传人'},\r\n                    {key: 'createTime', title: '上传日期'},\r\n                    {key: 'fileSize', title: '文件大小(KB)'},\r\n                    {\r\n                        title: '操作',\r\n                        key: 'action',\r\n                        width: 200,\r\n                        align: 'center',\r\n                        fixed: 'right',\r\n                        render: (h, params) => {\r\n                            let down =\r\n                                h('Button', {\r\n                                    props: {\r\n                                        type: 'primary',\r\n                                        size: 'small'\r\n                                    },\r\n                                    style: {\r\n                                        marginRight: '3px'\r\n                                    },\r\n                                    on: {\r\n                                        click: () => {\r\n                                            this.download(params.row)\r\n                                        }\r\n                                    }\r\n                                }, '下载')\r\n                            let remove = h('Button', {\r\n                                    props: {\r\n                                        type: 'error',\r\n                                        size: 'small'\r\n                                    },\r\n                                    on: {\r\n                                        click: () => {\r\n                                            this.remove(params.row)\r\n                                        }\r\n                                    }\r\n                                }\r\n                                , '删除')\r\n                            let action = [down]\r\n                            if (!this.downloadOnly) {\r\n                                action.push(remove)\r\n                            }\r\n                            return h('div', action)\r\n                        }\r\n                    }\r\n                ],\r\n                param: {\r\n                    busiId: null,\r\n                    busiAlias: \"附件(台账)\",\r\n                    categoryCode: \"file\",\r\n                    areaCode: \"sc\"\r\n                }\r\n            }\r\n        },\r\n        methods: {\r\n            choose(pcid) {\r\n                this.imgUrl = null;\r\n                this.show = true\r\n                this.param.busiId = pcid + \"\";\r\n                this.$refs.attachList.query();\r\n                let that = this;\r\n                setTimeout(function () {\r\n                    if (that.$refs.attachList.insideData && that.$refs.attachList.insideData.length > 0)\r\n                        that.showPic(that.$refs.attachList.insideData[0])\r\n                }, 200);\r\n            },\r\n            cancle() {\r\n                this.show = false\r\n            }, add() {\r\n                this.display = true\r\n                this.attach.fileForm = {}\r\n            },\r\n            remove(row) {\r\n                this.$Modal.confirm({\r\n                    title: \"确认删除\",\r\n                    content: \"<p>确定删除吗？</p>\",\r\n                    onOk: () => {\r\n                        axios.request({\r\n                            url: '/common/attachments/remove',\r\n                            method: 'post',\r\n                            params: {ids: row.id}\r\n                        }).then(() => {\r\n                            this.$refs.attachList.query()\r\n                        })\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                })\r\n            },\r\n            reload() {\r\n                this.$refs.attachList.query()\r\n            },\r\n            upload() {\r\n                if (!this.attach.fileForm.file) {\r\n                    this.$Message.info({content: '请选择要上传的文件！'})\r\n                    this.$refs.attach.buttonLoading = false\r\n                    return\r\n                }\r\n                axios.request({\r\n                    url: '/common/attachments/uploadMultiFile',\r\n                    method: 'post',\r\n                    data: Object.assign({}, this.param, this.attach.fileForm)\r\n                }).then(() => {\r\n                    this.$refs.attach.buttonLoading = false\r\n                    this.display = false\r\n                    this.$refs.attachList.query()\r\n                })\r\n            },\r\n            download(row) {\r\n                axios.file({\r\n                    url: '/common/attachments/download',\r\n                    method: 'get',\r\n                    params: {\r\n                        id: row.id,\r\n                        shardKey: row.shardKey,\r\n                        thumbnail: ''\r\n                    }\r\n                }).then((res) => {\r\n                    const content = res\r\n                    const blob = new Blob([content])\r\n                    const fileName = row.fileName\r\n                    if ('download' in document.createElement('a')) { // 非IE下载\r\n                        const elink = document.createElement('a')\r\n                        elink.download = fileName\r\n                        elink.style.display = 'none'\r\n                        elink.href = URL.createObjectURL(blob)\r\n                        document.body.appendChild(elink)\r\n                        elink.click()\r\n                        URL.revokeObjectURL(elink.href) // 释放URL 对象\r\n                        document.body.removeChild(elink)\r\n                    } else { // IE10+下载\r\n                        navigator.msSaveBlob(blob, fileName)\r\n                    }\r\n                })\r\n            }, showPic(row) {\r\n                if (row.litimgUrl) {\r\n                    this.imgUrl = row.litimgUrl;\r\n                }\r\n                // 兼容minio迁移，直接构建预览URL\r\n                else if (row.objectName){\r\n                  const baseUrl = window.location.origin;\r\n                  // 直接构建预览URL：baseUrl + / + bucketName + / + objectName\r\n                  const previewUrl = `${baseUrl}/${row.bucketName}/${row.objectName}`;\r\n                  row.litimgUrl = previewUrl;\r\n                  this.imgUrl = previewUrl;\r\n\r\n                  let bgImg = new Image()\r\n                  bgImg.src = this.imgUrl // 获取背景图片的url\r\n                  bgImg.onerror = () => {\r\n                    row.litimgUrl = null;\r\n                    this.$Message.error(\"图片加载失败！！！\");\r\n                    this.loading = false;\r\n                  }\r\n                  bgImg.onload = () => { // 等背景图片加载成功后 去除loading\r\n                    this.loading = false;\r\n                  }\r\n                }\r\n                else {\r\n                    let sub = row.fileName.split(\".\");\r\n                    let key = row.mongodbFileId + \".\" + sub[sub.length - 1];\r\n                    this.loading = true;\r\n                    axios.request({\r\n                        url: '/common/attachments/share/' + key,\r\n                        method: 'get',\r\n                    }).then((res) => {\r\n                        row.litimgUrl = res.data.url;\r\n                        this.imgUrl = res.data.url;\r\n                        let bgImg = new Image()\r\n                        bgImg.src = this.imgUrl // 获取背景图片的url\r\n                        bgImg.onerror = () => {\r\n                            row.litimgUrl = null;\r\n                            this.$Message.error(\"图片加载失败！！！\");\r\n                            this.loading = false;\r\n                        }\r\n                        bgImg.onload = () => { // 等背景图片加载成功后 去除loading\r\n                            this.loading = false;\r\n                        }\r\n                        // this.imgUrl = \"http://objects.objs.paas.sc.ctc.com/s/28aa0721445452647828ccd2e3a0602a.jpg?token=65794a68624763694f694a49557a49314e694973496e523563434936496b705856434a392e65794a6964574e725a5851694f694a7a5932356f4c575a70624755694c434a6c654841694f6a45314e6a67334d4445334e6a5173496d3969616d566a64434936496a4934595745774e7a49784e4451314e4455794e6a51334f44493459324e6b4d6d557a595441324d444a684c6d70775a794a392e625765664946333557794e723236554d3239394b314b6c634f6763646b516f54356f72557238484f794e6b\";\r\n                    })\r\n                }\r\n            }\r\n        }, mounted() {\r\n            const that = this;\r\n            window.onresize = () => {\r\n                return (() => {\r\n                    window.screenWidth = document.body.clientWidth;\r\n                    that.screenWidth = window.screenWidth * 0.7;\r\n                })();\r\n            };\r\n            window.onresize();\r\n        }\r\n\r\n    }\r\n</script>\r\n\r\n<style scoped></style>\r\n"]}]}