13:14:05.875 [main] INFO  o.s.b.d.r.<PERSON>artApplicationListener - [onApplicationStartingEvent,88] - <PERSON><PERSON> disabled due to an agent-based reloader being active
13:14:06.354 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
13:14:06.645 [main] INFO  com.sccl.Application - [logStarting,55] - Starting Application using Java 1.8.0_421 on WIN-TVKN4A60TS0 with PID 29804 (E:\cl-project\ln-nenghao\brch-ln\sccl-web\target\classes started by Administrator in E:\cl-project\ln-nenghao\brch-ln)
13:14:06.646 [main] INFO  com.sccl.Application - [logStartupProfileInfo,668] - The following profiles are active: dev-ln
13:14:06.716 [main] INFO  o.s.b.d.r.ChangeableUrls - [logTo,255] - The Class-Path manifest attribute in E:\.m2\repository\com\sun\xml\ws\jaxws-tools\2.2.6\jaxws-tools-2.2.6.jar referenced one or more files that do not exist: file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxws-rt.jar,file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxb-xjc.jar
13:14:06.716 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
13:14:06.716 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
13:14:09.292 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
13:14:09.292 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
13:14:09.790 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 483 ms. Found 0 JPA repository interfaces.
13:14:09.811 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
13:14:09.811 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
13:14:09.981 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 169 ms. Found 0 MongoDB repository interfaces.
13:14:09.995 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
13:14:09.995 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
13:14:10.173 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 168 ms. Found 0 Redis repository interfaces.
13:14:10.700 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
13:14:10.768 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'shiroConfig' of type [com.sccl.config.ShiroConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:10.789 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'getEhCacheManager' of type [org.apache.shiro.cache.ehcache.EhCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:10.852 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,158] - Cache with name 'com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache' does not yet exist.  Creating now.
13:14:10.853 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,165] - Added EhCache named [com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache]
13:14:10.855 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'userRealm' of type [com.sccl.common.shiro.jwt.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:10.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:10.874 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'captchaValidateFilter' of type [com.sccl.common.shiro.web.filter.captcha.CaptchaValidateFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:10.911 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.105 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'druidMutilConfig' of type [com.sccl.framework.config.DruidMutilConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.154 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'bsConfig' of type [com.sccl.framework.config.BsConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.178 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'ecmDsProps' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.215 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dataSourceProperties' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.304 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dynamicDataSource' of type [com.sccl.framework.datasource.DynamicDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.309 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.313 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.320 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.325 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.330 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.331 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.339 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.344 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.345 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.348 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.357 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.360 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.445 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.508 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.525 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.538 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:11.543 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:12.022 [main] INFO  c.a.d.p.DruidDataSource - [init,983] - {dataSource-1} inited
13:14:12.287 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
13:14:12.343 [main] INFO  o.hibernate.Version - [logVersion,44] - HHH000412: Hibernate ORM core version 5.4.32.Final
13:14:12.531 [main] INFO  o.h.a.common.Version - [<clinit>,56] - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
13:14:12.650 [main] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
13:14:12.907 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
13:14:12.922 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [buildNativeEntityManagerFactory,437] - Initialized JPA EntityManagerFactory for persistence unit 'default'
13:14:12.964 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:12.975 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:12.981 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'transactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:12.986 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceInterceptor' of type [com.sccl.framework.common.transaction.TxAdviceInterceptor$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:13.016 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdvice' of type [org.springframework.transaction.interceptor.TransactionInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:13.017 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:14:13.574 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [initialize,108] - Tomcat initialized with port(s): 18080 (http)
13:14:13.596 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-18080"]
13:14:13.596 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:14:13.596 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.55]
13:14:13.747 [main] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring embedded WebApplicationContext
13:14:13.748 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - [prepareWebApplicationContext,289] - Root WebApplicationContext: initialization completed in 7031 ms
13:14:14.016 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
13:14:14.079 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
13:14:14.492 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [notifyKieModuleFound,152] - Found kmodule: file:/E:/cl-project/ln-nenghao/brch-ln/sccl-web/target/classes/META-INF/kmodule.xml
13:14:14.794 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [generatePomPropertiesFromPom,376] - Recursed up folders, found and used pom.xml E:\cl-project\ln-nenghao\brch-ln\sccl-web\pom.xml
13:14:16.328 [main] INFO  c.s.m.b.s.u.StationAuditUtil - [<clinit>,76] - accountService getBean is null
13:14:16.451 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-protocol.yml
13:14:16.478 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
13:14:18.924 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,169] - Using existing EHCache named [loginRecordCache]
13:14:19.518 [main] INFO  c.s.m.s.a.c.LocalCacheConfig - [localCacheManager,48] - 本地缓存注入完成，开启权限缓存，本地缓存长度：1000，过期时间：1800000min，策略：only_local
13:14:27.693 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
13:14:29.743 [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - [lambda$buildAutowiringMetadata$1,478] - Autowired annotation is not supported on static fields: private static org.springframework.web.client.RestTemplate com.sccl.modules.util.WorkflowUtil.restTemplate
13:14:30.391 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
13:14:30.597 [main] INFO  o.s.b.d.a.OptionalLiveReloadServer - [startServer,58] - LiveReload server is running on port 35729
13:14:30.637 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - [<init>,53] - Adding welcome page: class path resource [META-INF/resources/index.html]
13:14:31.500 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-18080"]
13:14:31.524 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [start,220] - Tomcat started on port(s): 18080 (http) with context path '/energy-cost'
13:14:31.525 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,147] - Context refreshed
13:14:31.541 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,150] - Found 5 custom documentation plugin(s)
13:14:31.907 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:14:32.471 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:14:32.802 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:14:33.105 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:14:33.136 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_1
13:14:33.163 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingGET_1
13:14:33.181 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addSaveUsingPOST_1
13:14:33.190 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_2
13:14:33.194 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editSaveUsingPOST_1
13:14:33.203 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
13:14:33.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
13:14:33.210 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingHEAD_1
13:14:33.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
13:14:33.214 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPUT_1
13:14:33.216 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPATCH_1
13:14:33.218 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingDELETE_1
13:14:33.220 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingOPTIONS_1
13:14:33.221 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingTRACE_1
13:14:33.225 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
13:14:33.228 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingGET_1
13:14:33.233 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingPOST_1
13:14:33.609 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
13:14:33.628 [main] INFO  com.sccl.Application - [logStarted,61] - Started Application in 27.77 seconds (JVM running for 32.55)
13:16:57.348 [http-nio-18080-exec-1] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:16:57.349 [http-nio-18080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,525] - Initializing Servlet 'dispatcherServlet'
13:16:57.352 [http-nio-18080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,547] - Completed initialization in 3 ms
13:17:02.914 [http-nio-18080-exec-2] INFO  c.s.c.hlog.HLogUtils - [writeLoginLog,121] - {"server":"sysPA","sysCode":"S000AN2021012048","remoteIp":"*************","loginTime":"2025-07-28 13:17:02.888","logAppId":"QX661449_scnh_NHF_sys_PA","serialNbr":"202507283679822888","loginStatus":"1","authType":"11","account":"libingran","userCode":"libingran"}
13:17:04.321 [http-nio-18080-exec-5] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数101
13:17:15.105 [http-nio-18080-exec-16] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,234] - 第一步耗时 :0.001s
13:17:15.424 [http-nio-18080-exec-16] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,240] - 第二步耗时 :0.319s
13:17:15.425 [http-nio-18080-exec-16] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,152] - 台账录入 baseresultList开始执行
13:17:16.861 [http-nio-18080-exec-16] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,160] - 台账录入 baseresultList执行结束
13:17:17.101 [http-nio-18080-exec-16] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,269] - 第三步耗时 :1.677s
13:17:17.101 [http-nio-18080-exec-16] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,271] - 设置固定参数
13:17:17.102 [http-nio-18080-exec-16] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,277] - 设置固定参数结束
13:17:17.102 [http-nio-18080-exec-16] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,290] - 第四步耗时 :0.001s
13:17:17.102 [http-nio-18080-exec-16] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,291] - 总耗时 :1.998s
13:17:22.711 [http-nio-18080-exec-18] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,152] - 台账录入 baseresultList开始执行
13:17:24.308 [http-nio-18080-exec-18] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,160] - 台账录入 baseresultList执行结束
13:17:36.169 [http-nio-18080-exec-19] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,152] - 台账录入 baseresultList开始执行
13:17:37.735 [http-nio-18080-exec-19] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,160] - 台账录入 baseresultList执行结束
13:18:36.204 [http-nio-18080-exec-24] INFO  c.s.m.b.a.c.AccountController - [selectListByParams,349] - 查询台账耗时0.366s:
13:18:36.435 [http-nio-18080-exec-25] INFO  c.s.m.b.a.c.AccountController - [allAccountTotal,449] - alallAccountTotal开始执行
13:18:36.449 [http-nio-18080-exec-25] INFO  c.s.m.b.a.c.AccountController - [allAccountTotal,455] - allAccountTotal执行完毕，耗时0.014s
13:30:01.452 [http-nio-18080-exec-35] INFO  c.s.c.hlog.HLogUtils - [writeLoginLog,121] - {"server":"sysPA","sysCode":"S000AN2021012048","remoteIp":"*************","loginTime":"2025-07-28 13:30:01.452","logAppId":"QX661449_scnh_NHF_sys_PA","serialNbr":"202507283680601452","loginStatus":"1","authType":"11","account":"sys","userCode":"sys"}
13:30:02.238 [http-nio-18080-exec-38] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数163
13:45:37.958 [http-nio-18080-exec-64] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,234] - 第一步耗时 :0.0s
13:45:38.007 [http-nio-18080-exec-64] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,240] - 第二步耗时 :0.049s
13:45:38.007 [http-nio-18080-exec-64] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,152] - 台账录入 baseresultList开始执行
13:45:38.039 [http-nio-18080-exec-64] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,160] - 台账录入 baseresultList执行结束
13:45:38.123 [http-nio-18080-exec-64] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,269] - 第三步耗时 :0.116s
13:45:38.124 [http-nio-18080-exec-64] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,271] - 设置固定参数
13:45:38.124 [http-nio-18080-exec-64] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,277] - 设置固定参数结束
13:45:38.124 [http-nio-18080-exec-64] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,290] - 第四步耗时 :0.001s
13:45:38.124 [http-nio-18080-exec-64] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,291] - 总耗时 :0.166s
13:46:08.444 [http-nio-18080-exec-76] INFO  c.s.c.hlog.HLogUtils - [writeLoginLog,121] - {"server":"sysPA","sysCode":"S000AN2021012048","remoteIp":"*************","loginTime":"2025-07-28 13:46:08.443","logAppId":"QX661449_scnh_NHF_sys_PA","serialNbr":"202507283681568443","loginStatus":"1","authType":"11","account":"libingran","userCode":"libingran"}
13:46:09.119 [http-nio-18080-exec-79] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数101
13:46:11.717 [http-nio-18080-exec-5] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,234] - 第一步耗时 :0.0s
13:46:12.098 [http-nio-18080-exec-5] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,240] - 第二步耗时 :0.381s
13:46:12.098 [http-nio-18080-exec-5] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,152] - 台账录入 baseresultList开始执行
13:46:14.014 [http-nio-18080-exec-5] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,160] - 台账录入 baseresultList执行结束
13:46:14.232 [http-nio-18080-exec-5] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,269] - 第三步耗时 :2.134s
13:46:14.232 [http-nio-18080-exec-5] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,271] - 设置固定参数
13:46:14.232 [http-nio-18080-exec-5] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,277] - 设置固定参数结束
13:46:14.232 [http-nio-18080-exec-5] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,290] - 第四步耗时 :0.0s
13:46:14.232 [http-nio-18080-exec-5] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,291] - 总耗时 :2.515s
13:46:18.184 [http-nio-18080-exec-22] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,234] - 第一步耗时 :0.001s
13:46:18.515 [http-nio-18080-exec-22] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,240] - 第二步耗时 :0.331s
13:46:18.515 [http-nio-18080-exec-22] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,152] - 台账录入 baseresultList开始执行
13:46:20.563 [http-nio-18080-exec-22] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,160] - 台账录入 baseresultList执行结束
13:46:20.771 [http-nio-18080-exec-22] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,269] - 第三步耗时 :2.256s
13:46:20.771 [http-nio-18080-exec-22] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,271] - 设置固定参数
13:46:20.771 [http-nio-18080-exec-22] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,277] - 设置固定参数结束
13:46:20.771 [http-nio-18080-exec-22] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,290] - 第四步耗时 :0.0s
13:46:20.772 [http-nio-18080-exec-22] INFO  c.s.m.b.a.c.AccountController - [selfAccountList,291] - 总耗时 :2.588s
14:07:40.456 [http-nio-18080-exec-75] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,152] - 台账录入 baseresultList开始执行
14:07:40.559 [http-nio-18080-exec-75] INFO  c.s.m.b.a.s.AccountServiceImpl - [selectSelfList,160] - 台账录入 baseresultList执行结束
17:36:04.371 [SpringContextShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [destroy,651] - Closing JPA EntityManagerFactory for persistence unit 'default'
17:36:04.499 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2024] - {dataSource-0} closing ...
